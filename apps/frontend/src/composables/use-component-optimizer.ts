import {
  markRaw,
  nextTick,
  onUnmounted,
  ref,
  type Ref,
  shallowRef,
  watch,
} from 'vue';

interface ComponentOptimizerConfig {
  /** 是否启用组件缓存 */
  enableCache?: boolean;
  /** 缓存大小限制 */
  cacheSize?: number;
  /** 是否启用懒加载 */
  enableLazyLoading?: boolean;
  /** 是否启用虚拟滚动 */
  enableVirtualScroll?: boolean;
  /** 是否启用防抖 */
  enableDebounce?: boolean;
  /** 防抖延迟时间 */
  debounceDelay?: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccess: number;
}

/**
 * 组件性能优化 Composable
 * 提供组件缓存、懒加载、防抖等优化功能
 */
export function useComponentOptimizer<T = any>(
  config: ComponentOptimizerConfig = {},
) {
  const {
    enableCache = true,
    cacheSize = 100,
    enableLazyLoading = true,
    enableVirtualScroll = false,
    enableDebounce = true,
    debounceDelay = 300,
  } = config;

  // 组件缓存
  const cache = new Map<string, CacheEntry<T>>();
  const cacheStats = ref({
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0,
  });

  // 懒加载状态
  const isVisible = ref(false);
  const intersectionObserver = ref<IntersectionObserver | null>(null);

  // 防抖状态
  const debouncedValue = ref<T>();
  const debounceTimer = ref<null | number>(null);

  // 虚拟滚动状态
  const virtualScrollData = ref({
    startIndex: 0,
    endIndex: 0,
    visibleItems: [] as T[],
  });

  // 缓存管理
  const setCache = (key: string, data: T) => {
    if (!enableCache) return;

    // 如果缓存已满，移除最少使用的项
    if (cache.size >= cacheSize) {
      const lruKey = findLRUKey();
      if (lruKey) {
        cache.delete(lruKey);
      }
    }

    cache.set(key, {
      data: markRaw(data), // 使用 markRaw 避免深度响应式
      timestamp: Date.now(),
      accessCount: 1,
      lastAccess: Date.now(),
    });

    updateCacheStats();
  };

  const getCache = (key: string): null | T => {
    if (!enableCache) return null;

    const entry = cache.get(key);
    if (entry) {
      entry.accessCount++;
      entry.lastAccess = Date.now();
      cacheStats.value.hits++;
      updateCacheStats();
      return entry.data;
    }

    cacheStats.value.misses++;
    updateCacheStats();
    return null;
  };

  const clearCache = () => {
    cache.clear();
    updateCacheStats();
  };

  const findLRUKey = (): null | string => {
    let lruKey: null | string = null;
    let oldestAccess = Date.now();

    for (const [key, entry] of cache.entries()) {
      if (entry.lastAccess < oldestAccess) {
        oldestAccess = entry.lastAccess;
        lruKey = key;
      }
    }

    return lruKey;
  };

  const updateCacheStats = () => {
    const total = cacheStats.value.hits + cacheStats.value.misses;
    cacheStats.value.size = cache.size;
    cacheStats.value.hitRate =
      total > 0 ? (cacheStats.value.hits / total) * 100 : 0;
  };

  // 懒加载功能
  const setupLazyLoading = (target: Ref<HTMLElement | null>) => {
    if (!enableLazyLoading || !('IntersectionObserver' in window)) {
      isVisible.value = true;
      return;
    }

    intersectionObserver.value = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          isVisible.value = entry.isIntersecting;
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      },
    );

    watch(
      target,
      (newTarget) => {
        if (intersectionObserver.value && newTarget) {
          intersectionObserver.value.observe(newTarget);
        }
      },
      { immediate: true },
    );
  };

  // 防抖功能
  const debounce = <Args extends any[]>(
    fn: (...args: Args) => void,
    delay: number = debounceDelay,
  ) => {
    return (...args: Args) => {
      if (debounceTimer.value) {
        clearTimeout(debounceTimer.value);
      }

      debounceTimer.value = window.setTimeout(() => {
        fn(...args);
      }, delay);
    };
  };

  const debouncedUpdate = debounce((value: T) => {
    debouncedValue.value = value;
  });

  // 虚拟滚动功能
  const setupVirtualScroll = (
    items: Ref<T[]>,
    itemHeight: number,
    containerHeight: number,
  ) => {
    if (!enableVirtualScroll) {
      virtualScrollData.value.visibleItems = items.value;
      return;
    }

    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const bufferSize = Math.ceil(visibleCount / 2);

    const updateVisibleItems = (scrollTop: number) => {
      const startIndex = Math.max(
        0,
        Math.floor(scrollTop / itemHeight) - bufferSize,
      );
      const endIndex = Math.min(
        items.value.length - 1,
        startIndex + visibleCount + bufferSize * 2,
      );

      virtualScrollData.value.startIndex = startIndex;
      virtualScrollData.value.endIndex = endIndex;
      virtualScrollData.value.visibleItems = items.value.slice(
        startIndex,
        endIndex + 1,
      );
    };

    return {
      updateVisibleItems,
      getVisibleItems: () => virtualScrollData.value.visibleItems,
      getStartIndex: () => virtualScrollData.value.startIndex,
      getEndIndex: () => virtualScrollData.value.endIndex,
    };
  };

  // 组件性能监控
  const performanceMetrics = ref({
    renderTime: 0,
    updateCount: 0,
    lastUpdate: 0,
  });

  const measureRenderTime = async (fn: () => Promise<void> | void) => {
    const startTime = performance.now();

    await fn();
    await nextTick();

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    performanceMetrics.value.renderTime = renderTime;
    performanceMetrics.value.updateCount++;
    performanceMetrics.value.lastUpdate = Date.now();

    if (renderTime > 16) {
      // 超过一帧的时间
      console.warn(
        `[Component Optimizer] Slow render detected: ${renderTime.toFixed(2)}ms`,
      );
    }

    return renderTime;
  };

  // 内存优化的响应式数据
  const createOptimizedRef = <V>(initialValue: V) => {
    // 对于大型对象，使用 shallowRef 避免深度响应式
    if (typeof initialValue === 'object' && initialValue !== null) {
      return shallowRef(initialValue);
    }
    return ref(initialValue);
  };

  // 批量更新优化
  const batchUpdates = (() => {
    const updates: (() => void)[] = [];
    let isScheduled = false;

    const flush = () => {
      const currentUpdates = updates.splice(0);
      isScheduled = false;

      currentUpdates.forEach((update) => {
        try {
          update();
        } catch (error) {
          console.error('[Component Optimizer] Batch update failed:', error);
        }
      });
    };

    return (update: () => void) => {
      updates.push(update);

      if (!isScheduled) {
        isScheduled = true;
        nextTick(flush);
      }
    };
  })();

  // 清理函数
  const cleanup = () => {
    if (intersectionObserver.value) {
      intersectionObserver.value.disconnect();
    }

    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value);
    }

    clearCache();
  };

  onUnmounted(cleanup);

  return {
    // 缓存相关
    setCache,
    getCache,
    clearCache,
    cacheStats,

    // 懒加载相关
    isVisible,
    setupLazyLoading,

    // 防抖相关
    debounce,
    debouncedUpdate,
    debouncedValue,

    // 虚拟滚动相关
    setupVirtualScroll,
    virtualScrollData,

    // 性能监控
    performanceMetrics,
    measureRenderTime,

    // 优化工具
    createOptimizedRef,
    batchUpdates,

    // 清理
    cleanup,
  };
}
