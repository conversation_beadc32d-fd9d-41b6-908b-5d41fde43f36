import { ref } from 'vue';

interface NetworkOptimizerConfig {
  /** 是否启用请求缓存 */
  enableCache?: boolean;
  /** 缓存过期时间（毫秒） */
  cacheExpiry?: number;
  /** 是否启用请求去重 */
  enableDeduplication?: boolean;
  /** 是否启用请求重试 */
  enableRetry?: boolean;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否启用请求优先级 */
  enablePriority?: boolean;
  /** 并发请求限制 */
  concurrencyLimit?: number;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  expiry: number;
}

interface RequestConfig {
  url: string;
  method?: string;
  data?: any;
  priority?: 'high' | 'low' | 'medium';
  cache?: boolean;
  retry?: boolean;
  timeout?: number;
}

interface NetworkStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
}

/**
 * 网络性能优化 Composable
 * 提供请求缓存、去重、重试、优先级管理等功能
 */
export function useNetworkOptimizer(config: NetworkOptimizerConfig = {}) {
  const {
    enableCache = true,
    cacheExpiry = 5 * 60 * 1000, // 5分钟
    enableDeduplication = true,
    enableRetry = true,
    maxRetries = 3,
    retryDelay = 1000,
    enablePriority = true,
    concurrencyLimit = 6,
  } = config;

  // 缓存存储
  const cache = new Map<string, CacheEntry>();

  // 请求去重
  const pendingRequests = new Map<string, Promise<any>>();

  // 请求队列（按优先级）
  const requestQueues = {
    high: [] as (() => Promise<any>)[],
    medium: [] as (() => Promise<any>)[],
    low: [] as (() => Promise<any>)[],
  };

  // 当前并发请求数
  const activeRequests = ref(0);

  // 网络状态
  const networkStats = ref<NetworkStats>({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageResponseTime: 0,
    successRate: 0,
    cacheHitRate: 0,
  });

  // 网络连接状态
  const connectionInfo = ref({
    online: navigator.onLine,
    effectiveType: '4g',
    downlink: 10,
    rtt: 100,
    saveData: false,
  });

  // 监听网络状态变化
  const updateConnectionInfo = () => {
    connectionInfo.value.online = navigator.onLine;

    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connectionInfo.value.effectiveType = connection.effectiveType || '4g';
      connectionInfo.value.downlink = connection.downlink || 10;
      connectionInfo.value.rtt = connection.rtt || 100;
      connectionInfo.value.saveData = connection.saveData || false;
    }
  };

  // 生成缓存键
  const generateCacheKey = (config: RequestConfig): string => {
    const { url, method = 'GET', data } = config;
    const dataStr = data ? JSON.stringify(data) : '';
    return `${method}:${url}:${dataStr}`;
  };

  // 检查缓存
  const getFromCache = (key: string): any | null => {
    if (!enableCache) return null;

    const entry = cache.get(key);
    if (entry && Date.now() < entry.expiry) {
      networkStats.value.cacheHits++;
      updateStats();
      return entry.data;
    }

    if (entry) {
      cache.delete(key); // 删除过期缓存
    }

    networkStats.value.cacheMisses++;
    updateStats();
    return null;
  };

  // 设置缓存
  const setCache = (key: string, data: any, customExpiry?: number) => {
    if (!enableCache) return;

    const expiry = Date.now() + (customExpiry || cacheExpiry);
    cache.set(key, { data, timestamp: Date.now(), expiry });

    // 限制缓存大小
    if (cache.size > 1000) {
      const oldestKey = [...cache.keys()][0];
      cache.delete(oldestKey);
    }
  };

  // 清理过期缓存
  const cleanExpiredCache = () => {
    const now = Date.now();
    for (const [key, entry] of cache.entries()) {
      if (now >= entry.expiry) {
        cache.delete(key);
      }
    }
  };

  // 请求重试逻辑
  const retryRequest = async (
    requestFn: () => Promise<any>,
    retries: number = maxRetries,
  ): Promise<any> => {
    try {
      return await requestFn();
    } catch (error) {
      if (retries > 0 && enableRetry) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        return retryRequest(requestFn, retries - 1);
      }
      throw error;
    }
  };

  // 处理请求队列
  const processQueue = async () => {
    if (activeRequests.value >= concurrencyLimit) return;

    // 按优先级处理请求
    const priorities: (keyof typeof requestQueues)[] = [
      'high',
      'medium',
      'low',
    ];

    for (const priority of priorities) {
      const queue = requestQueues[priority];
      if (queue.length > 0 && activeRequests.value < concurrencyLimit) {
        const requestFn = queue.shift()!;
        activeRequests.value++;

        try {
          await requestFn();
        } finally {
          activeRequests.value--;
          // 继续处理队列
          processQueue();
        }
        break;
      }
    }
  };

  // 优化的请求函数
  const optimizedRequest = async <T = any>(
    config: RequestConfig,
  ): Promise<T> => {
    const startTime = performance.now();
    networkStats.value.totalRequests++;

    try {
      const cacheKey = generateCacheKey(config);

      // 检查缓存
      if (config.cache !== false) {
        const cachedData = getFromCache(cacheKey);
        if (cachedData) {
          return cachedData;
        }
      }

      // 请求去重
      if (enableDeduplication && pendingRequests.has(cacheKey)) {
        return await pendingRequests.get(cacheKey)!;
      }

      // 创建请求函数
      const requestFn = async () => {
        // 根据网络状况调整超时时间
        const timeout = config.timeout || getAdaptiveTimeout();

        const response = await fetch(config.url, {
          method: config.method || 'GET',
          body: config.data ? JSON.stringify(config.data) : undefined,
          headers: {
            'Content-Type': 'application/json',
            ...getOptimizedHeaders(),
          },
          signal: AbortSignal.timeout(timeout),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // 缓存响应
        if (config.cache !== false) {
          setCache(cacheKey, data);
        }

        return data;
      };

      // 执行请求（带重试）
      const requestPromise =
        config.retry === false ? requestFn() : retryRequest(requestFn);

      // 添加到去重映射
      if (enableDeduplication) {
        pendingRequests.set(cacheKey, requestPromise);
      }

      const result = await requestPromise;

      // 更新统计
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      updateResponseTime(responseTime);
      networkStats.value.successfulRequests++;

      return result;
    } catch (error) {
      networkStats.value.failedRequests++;
      throw error;
    } finally {
      // 清理去重映射
      if (enableDeduplication) {
        const cacheKey = generateCacheKey(config);
        pendingRequests.delete(cacheKey);
      }
      updateStats();
    }
  };

  // 获取自适应超时时间
  const getAdaptiveTimeout = (): number => {
    const { effectiveType, rtt } = connectionInfo.value;

    switch (effectiveType) {
      case '2g': {
        return 20_000;
      }
      case '3g': {
        return 15_000;
      }
      case 'slow-2g': {
        return 30_000;
      }
      case '4g':
      default: {
        return Math.max(5000, rtt * 10);
      }
    }
  };

  // 获取优化的请求头
  const getOptimizedHeaders = () => {
    const headers: Record<string, string> = {};

    // 根据网络状况设置压缩
    if (connectionInfo.value.saveData) {
      headers['Accept-Encoding'] = 'gzip, deflate, br';
    }

    // 设置缓存控制
    if (
      connectionInfo.value.effectiveType === 'slow-2g' ||
      connectionInfo.value.effectiveType === '2g'
    ) {
      headers['Cache-Control'] = 'max-age=3600'; // 1小时缓存
    }

    return headers;
  };

  // 更新响应时间统计
  const updateResponseTime = (responseTime: number) => {
    const { totalRequests, averageResponseTime } = networkStats.value;
    const newAverage =
      (averageResponseTime * (totalRequests - 1) + responseTime) /
      totalRequests;
    networkStats.value.averageResponseTime = newAverage;
  };

  // 更新统计信息
  const updateStats = () => {
    const { totalRequests, successfulRequests, cacheHits, cacheMisses } =
      networkStats.value;

    networkStats.value.successRate =
      totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;

    const totalCacheRequests = cacheHits + cacheMisses;
    networkStats.value.cacheHitRate =
      totalCacheRequests > 0 ? (cacheHits / totalCacheRequests) * 100 : 0;
  };

  // 预加载资源
  const preloadResource = (
    url: string,
    type: 'fetch' | 'image' | 'script' | 'style' = 'fetch',
  ) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    link.as = type;

    if (type === 'script') {
      link.crossOrigin = 'anonymous';
    }

    document.head.append(link);
  };

  // 批量预加载
  const batchPreload = (
    urls: string[],
    type: 'fetch' | 'image' | 'script' | 'style' = 'fetch',
  ) => {
    urls.forEach((url) => preloadResource(url, type));
  };

  // 网络状态监听
  window.addEventListener('online', updateConnectionInfo);
  window.addEventListener('offline', updateConnectionInfo);

  if ('connection' in navigator) {
    (navigator as any).connection.addEventListener(
      'change',
      updateConnectionInfo,
    );
  }

  // 定期清理过期缓存
  setInterval(cleanExpiredCache, 60_000); // 每分钟清理一次

  // 初始化
  updateConnectionInfo();

  return {
    // 请求方法
    request: optimizedRequest,

    // 缓存管理
    getFromCache,
    setCache,
    clearCache: () => cache.clear(),
    cleanExpiredCache,

    // 预加载
    preloadResource,
    batchPreload,

    // 状态
    networkStats,
    connectionInfo,
    activeRequests,

    // 工具方法
    generateCacheKey,
    updateStats,
  };
}
