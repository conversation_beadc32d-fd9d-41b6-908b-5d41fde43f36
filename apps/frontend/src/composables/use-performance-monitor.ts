import { nextTick, onMounted, onUnmounted, ref } from 'vue';

interface PerformanceMetrics {
  // Core Web Vitals
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift

  // 自定义指标
  ttfb?: number; // Time to First Byte
  domContentLoaded?: number;
  loadComplete?: number;

  // 内存使用
  memoryUsage?: {
    limit: number;
    total: number;
    used: number;
  };

  // 渲染性能
  renderTime?: number;
  componentCount?: number;
}

interface PerformanceConfig {
  enableWebVitals?: boolean;
  enableMemoryMonitoring?: boolean;
  enableRenderTracking?: boolean;
  reportInterval?: number; // 上报间隔（毫秒）
  onReport?: (metrics: PerformanceMetrics) => void;
}

/**
 * 性能监控 Composable
 * 监控 Core Web Vitals、内存使用、渲染性能等指标
 */
export function usePerformanceMonitor(config: PerformanceConfig = {}) {
  const {
    enableWebVitals = true,
    enableMemoryMonitoring = true,
    enableRenderTracking = true,
    reportInterval = 30_000, // 30秒
    onReport,
  } = config;

  const metrics = ref<PerformanceMetrics>({});
  const isSupported = ref(false);
  const observer = ref<null | PerformanceObserver>(null);
  const reportTimer = ref<null | number>(null);

  // 检查浏览器支持
  const checkSupport = () => {
    isSupported.value = !!(
      window.performance &&
      window.PerformanceObserver &&
      window.performance.mark &&
      window.performance.measure
    );
  };

  // 监控 Core Web Vitals
  const observeWebVitals = () => {
    if (!enableWebVitals || !isSupported.value) return;

    try {
      // FCP (First Contentful Paint)
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(
          (entry) => entry.name === 'first-contentful-paint',
        );
        if (fcpEntry) {
          metrics.value.fcp = fcpEntry.startTime;
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });

      // LCP (Largest Contentful Paint)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        metrics.value.lcp = lastEntry.startTime;
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // FID (First Input Delay)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          metrics.value.fid = entry.processingStart - entry.startTime;
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // CLS (Cumulative Layout Shift)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
            metrics.value.cls = clsValue;
          }
        });
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      observer.value = fcpObserver; // 保存一个引用用于清理
    } catch (error) {
      console.warn(
        '[Performance Monitor] Web Vitals observation failed:',
        error,
      );
    }
  };

  // 监控内存使用
  const observeMemoryUsage = () => {
    if (!enableMemoryMonitoring) return;

    const updateMemoryMetrics = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        metrics.value.memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
        };
      }
    };

    updateMemoryMetrics();
    setInterval(updateMemoryMetrics, 5000); // 每5秒更新一次
  };

  // 监控渲染性能
  const observeRenderPerformance = () => {
    if (!enableRenderTracking) return;

    // 监控导航时间
    const navigationEntry = performance.getEntriesByType(
      'navigation',
    )[0] as PerformanceNavigationTiming;
    if (navigationEntry) {
      metrics.value.ttfb =
        navigationEntry.responseStart - navigationEntry.requestStart;
      metrics.value.domContentLoaded =
        navigationEntry.domContentLoadedEventEnd -
        navigationEntry.navigationStart;
      metrics.value.loadComplete =
        navigationEntry.loadEventEnd - navigationEntry.navigationStart;
    }

    // 监控组件渲染时间
    const measureRenderTime = () => {
      const startTime = performance.now();
      nextTick(() => {
        const endTime = performance.now();
        metrics.value.renderTime = endTime - startTime;
      });
    };

    measureRenderTime();
  };

  // 上报性能数据
  const reportMetrics = () => {
    if (onReport && Object.keys(metrics.value).length > 0) {
      onReport({ ...metrics.value });
    }
  };

  // 启动定时上报
  const startReporting = () => {
    if (reportInterval > 0) {
      reportTimer.value = window.setInterval(reportMetrics, reportInterval);
    }
  };

  // 停止监控
  const stopMonitoring = () => {
    if (observer.value) {
      observer.value.disconnect();
    }
    if (reportTimer.value) {
      clearInterval(reportTimer.value);
    }
  };

  // 获取性能评分
  const getPerformanceScore = () => {
    const { fcp, lcp, fid, cls } = metrics.value;
    let score = 100;

    // FCP 评分 (< 1.8s 为好)
    if (fcp) {
      if (fcp > 3000) score -= 20;
      else if (fcp > 1800) score -= 10;
    }

    // LCP 评分 (< 2.5s 为好)
    if (lcp) {
      if (lcp > 4000) score -= 25;
      else if (lcp > 2500) score -= 15;
    }

    // FID 评分 (< 100ms 为好)
    if (fid) {
      if (fid > 300) score -= 20;
      else if (fid > 100) score -= 10;
    }

    // CLS 评分 (< 0.1 为好)
    if (cls) {
      if (cls > 0.25) score -= 15;
      else if (cls > 0.1) score -= 8;
    }

    return Math.max(0, score);
  };

  // 获取性能建议
  const getPerformanceRecommendations = (): string[] => {
    const recommendations: string[] = [];
    const { fcp, lcp, fid, cls, memoryUsage } = metrics.value;

    if (fcp && fcp > 1800) {
      recommendations.push(
        '优化首次内容绘制时间：减少关键资源大小，使用资源预加载',
      );
    }

    if (lcp && lcp > 2500) {
      recommendations.push(
        '优化最大内容绘制时间：优化图片加载，减少渲染阻塞资源',
      );
    }

    if (fid && fid > 100) {
      recommendations.push(
        '优化首次输入延迟：减少主线程阻塞，优化 JavaScript 执行',
      );
    }

    if (cls && cls > 0.1) {
      recommendations.push(
        '优化累积布局偏移：为图片和广告设置尺寸，避免动态内容插入',
      );
    }

    if (memoryUsage && memoryUsage.used / memoryUsage.limit > 0.8) {
      recommendations.push('内存使用率过高：检查内存泄漏，优化数据结构');
    }

    return recommendations;
  };

  onMounted(() => {
    checkSupport();
    if (isSupported.value) {
      observeWebVitals();
      observeMemoryUsage();
      observeRenderPerformance();
      startReporting();
    }
  });

  onUnmounted(() => {
    stopMonitoring();
  });

  return {
    metrics,
    isSupported,
    getPerformanceScore,
    getPerformanceRecommendations,
    reportMetrics,
    stopMonitoring,
  };
}
