import type { UserConfig } from 'vite';

async function getCommonConfig(): Promise<UserConfig> {
  return {
    build: {
      chunkSizeWarningLimit: 1500, // 降低警告阈值，鼓励更小的 chunk
      minify: 'terser',
      reportCompressedSize: false,
      sourcemap: false,
      terserOptions: {
        compress: {
          booleans: true, // 布尔值优化
          collapse_vars: true, // 变量折叠
          conditionals: true, // 条件优化
          dead_code: true, // 死代码消除
          drop_console: true,
          drop_debugger: true,
          evaluate: true, // 表达式求值
          hoist_funs: true,
          if_return: true, // if-return 优化
          inline: true, // 内联函数
          join_vars: true, // 变量合并
          loops: true, // 循环优化
          passes: 3, // 增加压缩轮数
          pure_funcs: ['console.log', 'console.info', 'console.warn'], // 移除特定函数调用
          pure_getters: true, // 假设属性访问无副作用
          reduce_vars: true, // 变量内联
          sequences: true, // 语句序列化
          side_effects: false, // 移除无副作用的语句
          unsafe: true, // 启用不安全优化
          unsafe_comps: true, // 不安全的比较优化
          unsafe_math: true, // 不安全的数学优化
          unsafe_methods: true, // 不安全的方法优化
          unsafe_proto: true, // 不安全的原型优化
          unsafe_regexp: true, // 不安全的正则优化
          unused: true, // 移除未使用代码
        },
        format: {
          ascii_only: true, // 确保输出只包含 ASCII 字符
          beautify: false,
          braces: false,
          comments: false,
          quote_style: 1,
          semicolons: false,
        },
        mangle: {
          properties: {
            regex: /^_/, // 混淆以下划线开头的属性
          },
          reserved: ['$', 'exports', 'require'], // 保留关键字
          safari10: true,
          toplevel: true,
        },
        module: true, // ES6 模块优化
        toplevel: true, // 顶级作用域优化
      },
    },
    esbuild: {
      charset: 'utf8',
      drop: ['console', 'debugger'], // 移除 console 和 debugger
      keepNames: false, // 不保留函数名
      legalComments: 'none',
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true,
      pure: ['console.log', 'console.info', 'console.warn'], // ESBuild 纯函数标记
      treeShaking: true,
    },
  };
}

export { getCommonConfig };
