import type { PluginOption } from 'vite';

import fs from 'node:fs';
import path from 'node:path';

interface BundleAnalyzerOptions {
  analyzeMode?: 'json' | 'server' | 'static';
  enabled?: boolean;
  generateStatsFile?: boolean;
  logLevel?: 'error' | 'info' | 'silent' | 'warn';
  openAnalyzer?: boolean;
  outputDir?: string;
  reportFilename?: string;
}

interface ChunkInfo {
  exports: string[];
  gzipSize?: number;
  imports: string[];
  modules: string[];
  name: string;
  size: number;
}

interface BundleStats {
  assets: Array<{
    name: string;
    size: number;
    type: string;
  }>;
  chunks: ChunkInfo[];
  duplicateModules: string[];
  largeChunks: string[];
  recommendations: string[];
  totalGzipSize: number;
  totalSize: number;
}

/**
 * 高级 Bundle 分析插件
 * 提供详细的构建产物分析和优化建议
 */
export const viteBundleAnalyzerPlugin = (
  options: BundleAnalyzerOptions = {},
): PluginOption => {
  const {
    analyzeMode = 'static',
    enabled = true,
    generateStatsFile = true,
    logLevel = 'info',
    openAnalyzer = false,
    outputDir = 'dist',
    reportFilename = 'bundle-report.html',
  } = options;

  if (!enabled) {
    return null;
  }

  let buildStats: BundleStats;
  let resolvedConfig: any;

  const log = (level: string, message: string) => {
    if (logLevel === 'silent') return;
    if (level === 'info' && ['error', 'warn'].includes(logLevel)) return;
    if (level === 'warn' && logLevel === 'error') return;

    const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
    console.log(`${prefix} [Bundle Analyzer] ${message}`);
  };

  return {
    apply: 'build',
    configResolved(config) {
      resolvedConfig = config;
    },

    generateBundle(_, bundle) {
      const chunks: ChunkInfo[] = [];
      const assets: Array<{ name: string; size: number; type: string }> = [];
      const moduleMap = new Map<string, string[]>();
      let totalSize = 0;
      const totalGzipSize = 0;

      // 分析 chunks
      Object.entries(bundle).forEach(([fileName, chunk]) => {
        if (chunk.type === 'chunk') {
          const chunkSize = chunk.code.length;
          totalSize += chunkSize;

          const chunkInfo: ChunkInfo = {
            exports: chunk.exports || [],
            imports: chunk.imports || [],
            modules: Object.keys(chunk.modules || {}),
            name: fileName,
            size: chunkSize,
          };

          chunks.push(chunkInfo);

          // 记录模块使用情况
          Object.keys(chunk.modules || {}).forEach((modulePath) => {
            if (!moduleMap.has(modulePath)) {
              moduleMap.set(modulePath, []);
            }
            moduleMap.get(modulePath)!.push(fileName);
          });
        } else if (chunk.type === 'asset') {
          const assetSize = chunk.source
            ? typeof chunk.source === 'string'
              ? chunk.source.length
              : chunk.source.length
            : 0;

          totalSize += assetSize;

          assets.push({
            name: fileName,
            size: assetSize,
            type: path.extname(fileName).slice(1) || 'unknown',
          });
        }
      });

      // 查找重复模块
      const duplicateModules = [...moduleMap.entries()]
        .filter(([_, chunks]) => chunks.length > 1)
        .map(([modulePath]) => modulePath);

      // 查找大型 chunks
      const largeChunks = chunks
        .filter((chunk) => chunk.size > 500 * 1024) // 500KB
        .map((chunk) => chunk.name);

      // 生成优化建议
      const recommendations: string[] = [];

      if (duplicateModules.length > 0) {
        recommendations.push(
          `发现 ${duplicateModules.length} 个重复模块，考虑优化代码分割策略`,
        );
      }

      if (largeChunks.length > 0) {
        recommendations.push(
          `发现 ${largeChunks.length} 个大型 chunk，建议进一步拆分`,
        );
      }

      const avgChunkSize = chunks.length > 0 ? totalSize / chunks.length : 0;
      if (avgChunkSize > 300 * 1024) {
        // 300KB
        recommendations.push('平均 chunk 大小过大，建议增加代码分割粒度');
      }

      buildStats = {
        assets,
        chunks,
        duplicateModules,
        largeChunks,
        recommendations,
        totalGzipSize,
        totalSize,
      };
    },

    name: 'vite:bundle-analyzer',

    writeBundle() {
      if (!buildStats) return;

      const outputPath = path.resolve(resolvedConfig.root, outputDir);

      // 生成统计文件
      if (generateStatsFile) {
        const statsPath = path.join(outputPath, 'bundle-stats.json');
        fs.writeFileSync(statsPath, JSON.stringify(buildStats, null, 2));
        log('info', `Bundle 统计文件已生成: ${statsPath}`);
      }

      // 生成 HTML 报告
      if (analyzeMode === 'static') {
        const reportPath = path.join(outputPath, reportFilename);
        const htmlReport = generateHTMLReport(buildStats);
        fs.writeFileSync(reportPath, htmlReport);
        log('info', `Bundle 分析报告已生成: ${reportPath}`);

        if (openAnalyzer) {
          const { exec } = require('node:child_process');
          exec(`open ${reportPath}`);
        }
      }

      // 输出控制台报告
      logConsoleReport(buildStats, log);
    },
  };
};

function generateHTMLReport(stats: BundleStats): string {
  const { assets, chunks, recommendations, totalSize } = stats;

  const formatSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / 1024 ** i).toFixed(2)} ${sizes[i]}`;
  };

  return `
<!DOCTYPE html>
<html>
<head>
  <title>Bundle Analysis Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    .section { margin-bottom: 30px; }
    .chunk { background: #fff; border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 3px; }
    .large { border-left: 4px solid #ff6b6b; }
    .medium { border-left: 4px solid #ffa726; }
    .small { border-left: 4px solid #66bb6a; }
    .recommendations { background: #e3f2fd; padding: 15px; border-radius: 5px; }
    table { width: 100%; border-collapse: collapse; }
    th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
  </style>
</head>
<body>
  <h1>Bundle Analysis Report</h1>
  
  <div class="summary">
    <h2>总览</h2>
    <p><strong>总大小:</strong> ${formatSize(totalSize)}</p>
    <p><strong>Chunks 数量:</strong> ${chunks.length}</p>
    <p><strong>Assets 数量:</strong> ${assets.length}</p>
  </div>

  <div class="section">
    <h2>Chunks 分析</h2>
    ${chunks
      .map((chunk) => {
        const sizeClass =
          chunk.size > 500 * 1024
            ? 'large'
            : (chunk.size > 200 * 1024
              ? 'medium'
              : 'small');
        return `
        <div class="chunk ${sizeClass}">
          <h3>${chunk.name}</h3>
          <p><strong>大小:</strong> ${formatSize(chunk.size)}</p>
          <p><strong>模块数:</strong> ${chunk.modules.length}</p>
          <p><strong>导入:</strong> ${chunk.imports.length}</p>
          <p><strong>导出:</strong> ${chunk.exports.length}</p>
        </div>
      `;
      })
      .join('')}
  </div>

  <div class="section">
    <h2>Assets 分析</h2>
    <table>
      <thead>
        <tr>
          <th>文件名</th>
          <th>类型</th>
          <th>大小</th>
        </tr>
      </thead>
      <tbody>
        ${assets
          .map(
            (asset) => `
          <tr>
            <td>${asset.name}</td>
            <td>${asset.type}</td>
            <td>${formatSize(asset.size)}</td>
          </tr>
        `,
          )
          .join('')}
      </tbody>
    </table>
  </div>

  ${
    recommendations.length > 0
      ? `
    <div class="recommendations">
      <h2>优化建议</h2>
      <ul>
        ${recommendations.map((rec) => `<li>${rec}</li>`).join('')}
      </ul>
    </div>
  `
      : ''
  }
</body>
</html>
  `;
}

function logConsoleReport(stats: BundleStats, log: Function) {
  const { chunks, duplicateModules, largeChunks, recommendations, totalSize } =
    stats;

  log('info', '='.repeat(50));
  log('info', 'Bundle 分析报告');
  log('info', '='.repeat(50));
  log('info', `总大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
  log('info', `Chunks 数量: ${chunks.length}`);

  if (largeChunks.length > 0) {
    log('warn', `大型 Chunks (>500KB): ${largeChunks.join(', ')}`);
  }

  if (duplicateModules.length > 0) {
    log('warn', `重复模块数量: ${duplicateModules.length}`);
  }

  if (recommendations.length > 0) {
    log('info', '优化建议:');
    recommendations.forEach((rec) => log('info', `  • ${rec}`));
  }

  log('info', '='.repeat(50));
}
