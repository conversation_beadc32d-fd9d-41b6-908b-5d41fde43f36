import type { PluginOption } from 'vite';

import type {
  ApplicationPluginOptions,
  CommonPluginOptions,
  ConditionPlugin,
  LibraryPluginOptions,
} from '../typing';

import { visualizer as viteVisualizerPlugin } from 'rollup-plugin-visualizer';
import viteCompressPlugin from 'vite-plugin-compression';
import viteDtsPlugin from 'vite-plugin-dts';
import { libInjectCss as viteLibInjectCss } from 'vite-plugin-lib-inject-css';
import { VitePWA } from 'vite-plugin-pwa';
import viteHtmlPlugin from 'vite-plugin-simple-html';

import viteVueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
import viteVue from '@vitejs/plugin-vue';

import { viteArchiverPlugin } from './archiver';
import { viteBundleAnalyzerPlugin } from './bundle-analyzer';
import { viteExtraAppConfigPlugin } from './extra-app-config';
import { viteImportMapPlugin } from './importmap';
import { viteInjectAppLoadingPlugin } from './inject-app-loading';
import { viteMetadataPlugin } from './inject-metadata';
import { viteNodePolyfillsPlugin } from './node-polyfills';
import { vitePrintPlugin } from './print';
import { viteSmartPreloadPlugin } from './smart-preload';
import {
  viteStaticAssetsOptimizerPlugin,
  viteTradingViewOptimizerPlugin,
} from './tradingview-optimizer';
import { viteVxeTableImportsPlugin } from './vxe-table';

/**
 * 获取条件成立的 vite 插件
 * @param conditionPlugins
 */
async function loadConditionPlugins(conditionPlugins: ConditionPlugin[]) {
  const plugins: PluginOption[] = [];
  for (const conditionPlugin of conditionPlugins) {
    if (conditionPlugin.condition) {
      const realPlugins = await conditionPlugin.plugins();
      plugins.push(...realPlugins);
    }
  }
  return plugins.flat();
}

/**
 * 根据条件获取通用的vite插件
 */
async function loadCommonPlugins(
  options: CommonPluginOptions,
): Promise<ConditionPlugin[]> {
  const { injectMetadata, isBuild, visualizer } = options;
  return [
    {
      condition: true,
      plugins: () => [
        viteVue({
          script: {
            defineModel: true,
            // propsDestructure: true,
          },
        }),
      ],
    },

    {
      condition: injectMetadata,
      plugins: async () => [await viteMetadataPlugin()],
    },
    {
      condition: isBuild && !!visualizer,
      plugins: () => [<PluginOption>viteVisualizerPlugin({
          filename: './node_modules/.cache/visualizer/stats.html',
          gzipSize: true,
          open: true,
        })],
    },
  ];
}

/**
 * 根据条件获取应用类型的vite插件
 */
async function loadApplicationPlugins(
  options: ApplicationPluginOptions,
): Promise<PluginOption[]> {
  // 单独取，否则commonOptions拿不到
  const isBuild = options.isBuild;
  const env = options.env;

  const {
    archiver,
    archiverPluginOptions,
    compress,
    compressTypes,
    extraAppConfig,
    html,
    htmlData,
    i18n,
    importmap,
    importmapOptions,
    injectAppLoading,
    print,
    printInfoMap,
    pwa,
    pwaOptions,
    staticAssetsOptimizer,
    tradingViewOptimizer,
    vxeTableLazyImport,
    ...commonOptions
  } = options;

  const commonPlugins = await loadCommonPlugins(commonOptions);

  return await loadConditionPlugins([
    {
      condition: true,
      plugins: () => [viteNodePolyfillsPlugin()],
    },
    ...commonPlugins,
    {
      condition: i18n,
      plugins: async () => {
        return [
          viteVueI18nPlugin({
            compositionOnly: true,
            fullInstall: true,
            runtimeOnly: true,
          }),
        ];
      },
    },
    {
      condition: print,
      plugins: async () => {
        return [await vitePrintPlugin({ infoMap: printInfoMap })];
      },
    },
    {
      condition: vxeTableLazyImport,
      plugins: async () => {
        return [await viteVxeTableImportsPlugin()];
      },
    },
    {
      condition: injectAppLoading,
      plugins: async () => [await viteInjectAppLoadingPlugin(!!isBuild, env)],
    },
    {
      condition: pwa,
      plugins: () =>
        VitePWA({
          registerType: 'autoUpdate',
          ...pwaOptions,
          disable: !isBuild,
          manifest: {
            background_color: '#0d0d10',
            display: 'standalone',
            start_url: '/',
            theme_color: '#0d0d10',
            ...pwaOptions?.manifest,
          },
          workbox: {
            additionalManifestEntries: [
              { revision: `${Date.now()}`, url: 'index.html' },
            ],
            cleanupOutdatedCaches: true,
            clientsClaim: true,
            globIgnores: ['**/node_modules/**/*'],
            globPatterns: ['**/*.{js,css,ico,png,svg,woff2}'],
            maximumFileSizeToCacheInBytes: 50 * 1024 * 1024,
            mode: isBuild ? 'production' : 'development',
            navigateFallback: 'index.html',
            navigateFallbackDenylist: [
              /^\/_/,
              /\/api\//,
              /\.json$/,
              /\.(js|css|png|jpg|svg|ico|woff2|ttf|mp3)$/,
            ],
            runtimeCaching: [
              // 静态资源 - 缓存优先策略
              {
                handler: 'CacheFirst',
                options: {
                  cacheName: 'static-resources',
                  expiration: {
                    maxAgeSeconds: 60 * 60 * 24 * 30, // 30天
                    maxEntries: 200,
                  },
                  plugins: [
                    {
                      cacheKeyWillBeUsed: async ({
                        request,
                      }: {
                        request: Request;
                      }) => {
                        // 移除查询参数以提高缓存命中率
                        const url = new URL(request.url);
                        url.search = '';
                        return url.href;
                      },
                    },
                  ],
                },
                urlPattern: /\.(js|css|woff2|woff|ttf|eot)$/,
              },
              // 图片资源 - 缓存优先策略
              {
                handler: 'CacheFirst',
                options: {
                  cacheName: 'image-cache',
                  expiration: {
                    maxAgeSeconds: 60 * 60 * 24 * 30, // 30天
                    maxEntries: 300,
                  },
                  plugins: [
                    {
                      cacheKeyWillBeUsed: async ({ request }) => {
                        // 为图片添加版本控制
                        const url = new URL(request.url);
                        if (!url.searchParams.has('v')) {
                          url.searchParams.set('v', '1');
                        }
                        return url.href;
                      },
                    },
                  ],
                },
                urlPattern: /\.(png|jpe?g|svg|gif|webp|ico|avif)$/,
              },
              // 音频文件
              {
                handler: 'CacheFirst',
                options: {
                  cacheName: 'audio-cache',
                  expiration: {
                    maxAgeSeconds: 60 * 60 * 24 * 7, // 7天
                    maxEntries: 50,
                  },
                },
                urlPattern: /\.(mp3|wav|ogg|m4a)$/,
              },
              // API 请求 - 网络优先策略
              {
                handler: 'NetworkFirst',
                options: {
                  cacheName: 'api-cache',
                  expiration: {
                    maxAgeSeconds: 60 * 10, // 10分钟
                    maxEntries: 100,
                  },
                  networkTimeoutSeconds: 3,
                  plugins: [
                    {
                      cacheWillUpdate: async ({ response }) => {
                        // 只缓存成功的响应
                        return response.status === 200;
                      },
                    },
                  ],
                },
                urlPattern: /\/api\//,
              },
              // WebSocket 和实时数据 - 仅网络策略
              {
                handler: 'NetworkOnly',
                urlPattern: /\/(ws|websocket)\//,
              },
              // 第三方资源 - 网络优先策略
              {
                handler: 'NetworkFirst',
                options: {
                  cacheName: 'external-cache',
                  expiration: {
                    maxAgeSeconds: 60 * 60 * 24, // 1天
                    maxEntries: 50,
                  },
                  networkTimeoutSeconds: 5,
                },
                urlPattern: /^https:\/\/(?!silkbot\.org|surgebot\.io)/,
              },
              // HTML 页面 - 网络优先策略
              {
                handler: 'NetworkFirst',
                options: {
                  cacheName: 'pages-cache',
                  expiration: {
                    maxAgeSeconds: 60 * 60 * 24, // 1天
                    maxEntries: 20,
                  },
                  networkTimeoutSeconds: 3,
                },
                urlPattern: /\.html$/,
              },
            ],
            skipWaiting: true,
          },
        }),
    },
    {
      condition: isBuild && !!compress,
      plugins: () => {
        const compressPlugins: PluginOption[] = [];
        if (compressTypes?.includes('brotli')) {
          compressPlugins.push(
            viteCompressPlugin({ deleteOriginFile: false, ext: '.br' }),
          );
        }
        if (compressTypes?.includes('gzip')) {
          compressPlugins.push(
            viteCompressPlugin({ deleteOriginFile: false, ext: '.gz' }),
          );
        }
        return compressPlugins;
      },
    },
    {
      condition: !!html,
      plugins: () => [
        viteHtmlPlugin({ inject: { data: htmlData }, minify: true }),
      ],
    },
    {
      condition: isBuild && importmap,
      plugins: () => {
        return [viteImportMapPlugin(importmapOptions)];
      },
    },
    {
      condition: isBuild && extraAppConfig,
      plugins: async () => [
        await viteExtraAppConfigPlugin({ isBuild: true, root: process.cwd() }),
      ],
    },
    {
      condition: archiver,
      plugins: async () => {
        return [await viteArchiverPlugin(archiverPluginOptions)];
      },
    },
    // TradingView 优化插件
    {
      condition: !!tradingViewOptimizer,
      plugins: () => {
        const options =
          typeof tradingViewOptimizer === 'object' ? tradingViewOptimizer : {};
        return [viteTradingViewOptimizerPlugin(options)];
      },
    },
    // 静态资源优化插件
    {
      condition: !!staticAssetsOptimizer,
      plugins: () => [viteStaticAssetsOptimizerPlugin()],
    },
    // Bundle 分析插件
    {
      condition: isBuild && (visualizer || process.env.ANALYZE === 'true'),
      plugins: () => [
        viteBundleAnalyzerPlugin({
          enabled: true,
          generateStatsFile: true,
          logLevel: 'info',
          openAnalyzer: process.env.ANALYZE === 'true',
        }),
      ],
    },
    // 智能预加载插件
    {
      condition: isBuild,
      plugins: () => [
        viteSmartPreloadPlugin({
          delay: 2000,
          enabled: true,
          networkConditions: {
            disableOnDataSaver: true,
            disableOnSlowNetwork: true,
          },
          onIdle: true,
          strategy: 'critical',
        }),
      ],
    },
  ]);
}

/**
 * 根据条件获取库类型的vite插件
 */
async function loadLibraryPlugins(
  options: LibraryPluginOptions,
): Promise<PluginOption[]> {
  // 单独取，否则commonOptions拿不到
  const isBuild = options.isBuild;
  const { dts, injectLibCss, ...commonOptions } = options;
  const commonPlugins = await loadCommonPlugins(commonOptions);
  return await loadConditionPlugins([
    ...commonPlugins,
    {
      condition: isBuild && !!dts,
      plugins: () => [viteDtsPlugin({ logLevel: 'error' })],
    },
    {
      condition: injectLibCss,
      plugins: () => [viteLibInjectCss()],
    },
  ]);
}

export {
  loadApplicationPlugins,
  loadLibraryPlugins,
  viteArchiverPlugin,
  viteCompressPlugin,
  viteDtsPlugin,
  viteHtmlPlugin,
  viteVisualizerPlugin,
};

export {
  viteStaticAssetsOptimizerPlugin,
  viteTradingViewOptimizerPlugin,
} from './tradingview-optimizer';
